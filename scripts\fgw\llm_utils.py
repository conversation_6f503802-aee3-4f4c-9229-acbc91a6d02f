import requests

class SiliconFlowClient:
    """
    硅基流动大模型通用调用工具类
    """
    API_URL = "https://api.siliconflow.cn/v1/chat/completions"
    API_TOKEN = "sk-ipdokaviyzsewrabfsbpdkogjduaauquvitfaprhfoikvhab"
    MODEL = "deepseek-ai/DeepSeek-V3"

    @staticmethod
    def call_llm(text, prompt=None):
        """
        通用大模型调用，传入文本，返回摘要/结果。
        prompt: 可选，若不传则用默认摘要提示。
        """
        if prompt is None:
            prompt = "请用中文简要概括以下内容，50字以内："
        user_content = f"{prompt}\n{text}"
        
        # 简化请求参数，只保留必要字段
        payload = {
            "model": SiliconFlowClient.MODEL,
            "messages": [
                {"role": "user", "content": user_content}
            ],
            "max_tokens": 512
        }
        
        headers = {
            "Authorization": f"Bearer {SiliconFlowClient.API_TOKEN}",
            "Content-Type": "application/json"
        }
        
        try:
            resp = requests.post(SiliconFlowClient.API_URL, json=payload, headers=headers, timeout=30)
            resp.raise_for_status()
            data = resp.json()
            if "choices" in data and data["choices"]:
                return data["choices"][0]["message"]["content"].strip()
            return str(data)
        except requests.exceptions.HTTPError as e:
            print(f"大模型接口调用失败: {e}")
            if hasattr(e, 'response'):
                print(f"错误详情: {e.response.text}")
            return ""
        except Exception as e:
            print(f"大模型接口调用失败: {e}")
            return ""

def call_llm(text, prompt=None):
    """
    通用大模型调用顶层方法，便于主流程解耦。
    """
    return SiliconFlowClient.call_llm(text, prompt) 
