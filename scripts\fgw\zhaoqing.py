# https://drc.gd.gov.cn/gfxwj5633/index.html
import re
import requests
from bs4 import BeautifulSoup
import pandas as pd
import datetime
from scripts.fgw.llm_utils import call_llm
from global_logger import get_logger
logger = get_logger(__name__, log_path='scripts/logs/广东省发展和改革委员会-肇庆-公开政务数据-爬取任务.log')
from tqdm import tqdm
import os
import time

BASE_URL = "https://drc.gd.gov.cn"
LIST_URL = f"{BASE_URL}/gfxwj5633/index.html"

headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}

FIELDS = [
    "来源站点", "部门", "级别", "标题", "简介", "发布时间", "截止时间", "发文机关", "主题", "索引号", "文号", "详情地址", "正文内容", "敏感数字（元，%）", "附件", "文件名及链接"
]

def fetch_detail(url, title="", pub_date=""):
    """获取详情页内容"""
    resp = requests.get(url, headers=headers, timeout=10)
    resp.encoding = resp.apparent_encoding
    soup = BeautifulSoup(resp.text, "html.parser")
    
    # 从详情页获取标题
    title_elem = soup.find("h4", class_="wdtit")
    if title_elem:
        title = title_elem.get_text(strip=True)
    
    # 获取发布时间
    if not pub_date:
        jbxx = soup.find("div", class_="jbxx")
        if jbxx:
            time_span = jbxx.find_all("span")
            if len(time_span) > 1:
                time_text = time_span[1].get_text(strip=True)
                time_match = re.search(r'时间：(\d{4}-\d{2}-\d{2})', time_text)
                if time_match:
                    pub_date = time_match.group(1)
    
    # 获取正文内容
    content_div = soup.find("div", id="content1")
    content = ""
    if content_div:
        content = content_div.get_text("\n", strip=True)
    
    # 获取文号 - 从正文中提取
    doc_no = ""
    # 修正正则表达式，使用非捕获组来匹配多个可能的部门和文件类型
    doc_no_match = re.search(r'粤(?:发改|建|交|水|市监)(?:规|函|通|发)\s*[〔［\[\(（]?\d{4}[〕］\]\)）]?\s*\d+\s*号', content)
    if doc_no_match:
        doc_no = doc_no_match.group(0)
    
    # 敏感数字（元，%）
    sensitive = ", ".join(re.findall(r"[\d.]+\s*(?:元|%|％|万元|亿元)", content))
    
    # 附件
    attachment = ""
    attachment_div = soup.find("div", class_="fjlis")
    if attachment_div:
        attachment_links = attachment_div.find_all("a", href=True)
        for a in attachment_links:
            href = a.get("href")
            full_href = href if href.startswith("http") else BASE_URL + href
            attachment += f"{a.get_text(strip=True)}: {full_href}, "
    
    # 发文机关 - 从标题或正文提取
    issuing_agency = ""
    agency_match = re.search(r'广东省发展(?:和)?改革委员会', title)
    if agency_match:
        issuing_agency = agency_match.group(0)
    
    # 详情地址
    detail_url = url
    
    # 文件名及链接 - 这里只存储标题，实际的超链接会在保存Excel时创建
    file_link = title
    
    # 组装数据行
    row = [
        "广东省发展和改革委员会",  # 来源站点（固定值）
        "发改委",                # 部门（固定值）
        "肇庆",                  # 级别（固定值）
        title,                  # 标题
        "",                     # 简介（后面用大模型生成）
        pub_date,               # 发布时间
        "",                     # 截止时间
        issuing_agency,         # 发文机关
        "",                     # 主题
        "",                     # 索引号
        doc_no,                 # 文号
        detail_url,             # 详情地址
        content,                # 正文内容
        sensitive,              # 敏感数字
        attachment.rstrip(", "),# 附件
        file_link               # 文件名及链接
    ]
    return row

def fetch_list():
    """获取列表页内容"""
    resp = requests.get(LIST_URL, headers=headers, timeout=10)
    resp.encoding = resp.apparent_encoding
    soup = BeautifulSoup(resp.text, "html.parser")
    
    # 查找文件列表 - 获取最后一个匹配的ul元素
    all_uls = soup.find_all("ul", class_="comlist1 mt10")
    if not all_uls:
        logger.warning("未找到文件列表")
        return []
    
    # 使用最后一个匹配的ul元素
    ul = all_uls[-1]
    
    data = []
    for li in ul.find_all("li"):
        a = li.find("a")
        span = li.find("span")
        
        if a:
            title = a.get("title") or a.get_text(strip=True)
            href = a.get("href")
            full_url = href if href.startswith("http") else BASE_URL + href
            
            # 获取日期
            pub_date = ""
            if span:
                date_text = span.get_text(strip=True)
                date_match = re.search(r'\[(\d{4}-\d{2}-\d{2})\]', date_text)
                if date_match:
                    pub_date = date_match.group(1)
            
            try:
                row = fetch_detail(full_url, title, pub_date)
                logger.info(f"采集详情页: 标题={row[3]} 链接={full_url} 正文内容={row[12][:100]}...")
                data.append(row)
                # 添加延迟，避免请求过快
                time.sleep(1)
            except Exception as e:
                logger.error(f"详情页解析失败: {full_url}, 错误: {e}")
    
    # 用大模型生成简介
    logger.info("\n正在为每条数据生成简介（50字以内摘要）...")
    for row in tqdm(data, desc="生成简介", ncols=80):
        content = row[12]  # 正文内容
        if content:
            try:
                prompt = "请为以下文章内容生成一个50字以内的简要摘要，不要超过50字：\n\n" + content
                summary = call_llm(prompt)
                logger.info(f"生成简介: {summary}")
                # 确保摘要不超过50字
                if len(summary) > 50:
                    summary = summary[:47] + "..."
                row[4] = summary  # 简介列
            except Exception as e:
                logger.error(f"生成简介失败: {e}")
    
    return data

def save_to_excel(data):
    """保存数据到Excel"""
    if not data:
        logger.warning("没有数据可保存")
        return
    
    # 创建数据目录
    data_dir = os.path.join("scripts/fgw", "data")
    
    # 生成文件名
    py_name = os.path.splitext(os.path.basename(__file__))[0]
    filename = f"{py_name}.xlsx"
    
    # 完整文件路径
    filepath = os.path.join(data_dir, filename)
    
    # 创建DataFrame
    df = pd.DataFrame(data, columns=FIELDS)
    
    # 创建一个Excel writer对象
    writer = pd.ExcelWriter(filepath, engine='openpyxl')
    
    # 将DataFrame写入Excel
    df.to_excel(writer, index=False)
    
    # 获取工作表
    worksheet = writer.sheets['Sheet1']
    
    # 为"文件名及链接"列添加超链接
    for idx, row in enumerate(data, start=2):  # Excel行从2开始（1是标题行）
        title = row[3]  # 标题
        url = row[11]   # 详情地址
        cell = worksheet.cell(row=idx, column=16)  # 第16列是"文件名及链接"
        cell.value = title
        cell.hyperlink = url
        cell.style = 'Hyperlink'
    
    # 保存Excel
    writer.close()
    logger.info(f"已保存到 {filepath}")

def main():
    try:
        logger.info("开始抓取数据...")
        data = fetch_list()
        if not data:
            logger.warning("警告：未获取到任何数据！")
        else:
            logger.info(f"成功获取 {len(data)} 条数据")
        # 保存数据到Excel
        save_to_excel(data)
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
