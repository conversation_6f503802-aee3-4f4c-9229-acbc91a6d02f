import pandas as pd
import uuid
from datetime import datetime
from db_conn import config
import pymysql
import math

def safe_value(val):
    if val is None:
        return None
    if isinstance(val, float) and math.isnan(val):
        return None
    return val

def insert_xlsx_to_policy_info(filepath):
    df = pd.read_excel(filepath)
    records = df.to_dict(orient='records')
    conn = pymysql.connect(**config)
    try:
        with conn.cursor() as cursor:
            for row in records:
                id = str(uuid.uuid4()).replace('-', '')
                sql = """
                    INSERT INTO ptl_policy_info (
                        ID, SOURCE_SITE, DEPT, LEVEL, TITLE, SUMMARY, PUBLISH_TIME, END_TIME, ISSUING_AGENCY, TOPIC, INDEX_NO, DOC_NO, DETAIL_URL, CONTENT, SENSITIVE_NUMBERS, ATTACHMENT, FILE_LINK, CREATE_TIME
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                cursor.execute(sql, (
                    id,
                    safe_value(row.get('来源站点')),
                    safe_value(row.get('部门')),
                    safe_value(row.get('级别')),
                    safe_value(row.get('标题')),
                    safe_value(row.get('简介')),
                    safe_value(row.get('发布时间')),
                    safe_value(row.get('截止时间')),
                    safe_value(row.get('发文机关')),
                    safe_value(row.get('主题')),
                    safe_value(row.get('索引号')),
                    safe_value(row.get('文号')),
                    safe_value(row.get('详情地址')),
                    safe_value(row.get('正文内容')),
                    safe_value(row.get('数字（元，%）')),
                    safe_value(row.get('附件')),
                    safe_value(row.get('件名及链接')),
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                ))
            conn.commit()
    finally:
        conn.close()

insert_xlsx_to_policy_info('scripts/fgw/data/dezhou.xlsx')