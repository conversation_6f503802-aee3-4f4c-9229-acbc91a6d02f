import requests
from bs4 import BeautifulSoup
import pandas as pd
import datetime
import re
import os
import openpyxl
from global_logger import get_logger
logger = get_logger(__name__, log_path='scripts/logs/福建省发展和改革委员会-宁德 -公开政务数据-爬取任务.log')

FIELDS = [
    "来源站点", "部门", "级别", "标题", "简介", "发布时间", "截止时间", "发文机关", "主题", "索引号", "文号", "详情地址", "正文内容", "敏感数字（元，%）", "附件", "文件名及链接"
]

def fetch_list():
    url = "https://fgw.fujian.gov.cn/ztzl/ssxsdmyjjqszl/zcsd/"
    resp = requests.get(url, timeout=10)
    resp.encoding = resp.apparent_encoding
    soup = BeautifulSoup(resp.text, "html.parser")
    data = []
    for ul in soup.select(".list_base.mar_t_small ul"):
        for li in ul.find_all("li"):
            a = li.find("a")
            date_span = li.find("span")
            if a and date_span:
                href = a["href"] if a["href"].startswith("http") else "https://fgw.fujian.gov.cn" + a["href"]
                title = a.get("title", a.text.strip())
                pub_date = date_span.text.strip()
                row = fetch_detail(href, title, pub_date)
                logger.info(f"采集详情页: 标题={row[3]} 链接={href} 正文内容={row[12][:100]}...")
                data.append(row)
    return data

def fetch_detail(url, title, pub_date):
    try:
        resp = requests.get(url, timeout=10)
        resp.encoding = resp.apparent_encoding
        soup = BeautifulSoup(resp.text, "html.parser")
        main_title = soup.select_one(".article_title")
        sub_title = soup.select_one(".article_title_sec")
        article_time = soup.select_one(".article_time")
        article_views = soup.select_one(".article_views")
        article_source = soup.select_one(".article_source")
        content_elem = soup.select_one(".article_content")
        content = content_elem.get_text("\n", strip=True) if content_elem else ""
        # 简介
        summary = content[:100]
        # 敏感数字
        sensitive = ", ".join(re.findall(r"[\d.]+\s*(?:元|%|％)", content))
        # 附件
        attachment = ""
        attach_div = soup.select_one(".article_attachment")
        if attach_div:
            attach_links = attach_div.find_all("a", href=True)
            attach_urls = [a['href'] for a in attach_links]
            attachment = ", ".join(attach_urls)
        # 组装
        row = [
            "福建省发展和改革委员会",  # 来源站点
            "发改委",                # 部门
            "福建",                  # 级别
            main_title.text.strip() if main_title else title,  # 标题
            summary,                # 简介
            article_time.text.strip() if article_time else pub_date,  # 发布时间
            "",                     # 截止时间
            article_source.text.strip() if article_source else "",    # 发文机关
            "",                     # 主题
            "",                     # 索引号
            sub_title.text.strip() if sub_title else "",             # 文号
            url,                    # 详情地址
            content,                # 正文内容
            sensitive,              # 敏感数字
            attachment,             # 附件
            main_title.text.strip() if main_title else title         # 文件名及链接
        ]
        return row
    except Exception as e:
        print(f"详情页解析失败: {url}, 错误: {e}")
        return None

def save_to_excel(data):
    columns = FIELDS
    df = pd.DataFrame(data, columns=columns)
    py_name = os.path.splitext(os.path.basename(__file__))[0]
    filename = f"{py_name}.xlsx"
    folder = "scripts/fgw/data"
    if not os.path.exists(folder):
        os.makedirs(folder)
    filename = os.path.join(folder, f"{filename}")
    df.to_excel(filename, index=False, engine='openpyxl')
    wb = openpyxl.load_workbook(filename)
    ws = wb.active
    for idx, row in enumerate(data, start=2):
        title = row[3]
        url = row[11]
        cell = ws.cell(row=idx, column=16)
        cell.value = title
        cell.hyperlink = url
        cell.style = 'Hyperlink'
    wb.save(filename)
    print(f"已保存到 {filename}")

def main():
    data = fetch_list()
    save_to_excel(data)

if __name__ == "__main__":
    main()
