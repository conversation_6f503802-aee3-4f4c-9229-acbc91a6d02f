import asyncio
from playwright.async_api import async_playwright
import pandas as pd
import re
import openpyxl
from openpyxl.styles import Font, Border, Side

# 需采集的字段
FIELDS = [
    '来源站点', '部门', '级别', '标题', '简介', '发布时间', '截止时间', '发文机关', '主题', '索引号', '文号',
    '详情地址', '正文内容', '敏感数字', '附件', '文件名及链接'
]

LIST_URL = 'https://czt.ah.gov.cn/public/column/7041?type=4&action=list&nav=3&catId=6715461'
CONCURRENCY = 5  # 并发量可调

async def fetch_all_detail_urls(page):
    detail_urls = []
    page_num = 1
    stop = False
    while not stop:
        await page.wait_for_selector('ul.clearfix.xxgk_nav_list li')
        items = await page.query_selector_all('ul.clearfix.xxgk_nav_list li')
        for item in items:
            li_text = (await item.inner_text()).strip()
            match = re.search(r'(20\d{2}-\d{2}-\d{2})', li_text)
            pub_date = match.group(1) if match else ''
            print(f'li_text: {li_text}')
            print(f'pub_date: {pub_date}')
            if not pub_date.startswith('2025'):
                stop = True
                break
            a = await item.query_selector('a')
            href = await a.get_attribute('href') if a else None
            title = (await a.inner_text()).strip() if a else ''
            if href:
                detail_urls.append((page.url.rsplit('/', 1)[0] + '/' + href if not href.startswith('http') else href, title))
        if stop:
            break
        next_btn = await page.query_selector('a:has-text("下一页")')
        if next_btn and await next_btn.is_enabled():
            await next_btn.click()
            page_num += 1
            print(f'翻到第{page_num}页...')
            await page.wait_for_timeout(1500)
        else:
            break
    return detail_urls

async def extract_detail(page, url_title):
    url, list_title = url_title if isinstance(url_title, tuple) else (url_title, '')
    try:
        await page.goto(url)
        await page.wait_for_timeout(1000)
        data = {k: '' for k in FIELDS}
        data['详情地址'] = url
        data['来源站点'] = '安徽省财政厅'
        data['部门'] = ''
        data['级别'] = ''
        # 标题 优先用列表页a标签文本
        data['标题'] = list_title
        # 发布时间
        pub_elem = await page.query_selector('span.time, .pubtime, .date')
        if pub_elem:
            data['发布时间'] = (await pub_elem.inner_text()).strip()
        # 发文机关、主题、索引号、文号、截止时间
        info_elems = await page.query_selector_all('div.xxgk_info, .info, table tr')
        for elem in info_elems:
            text = (await elem.inner_text()).strip()
            for key in ['发文机关', '索引号', '文号', '截止时间']:
                if key in text:
                    value = text.split(key)[-1].strip(':： \n')
                    data[key] = value
            if '主题' in text or '主题分类' in text:
                value = text.split('主题')[-1].strip(':： \n') if '主题' in text else text.split('主题分类')[-1].strip(':： \n')
                data['主题'] = value if value else ''
        # 简介
        summary_elem = await page.query_selector('meta[name="description"]')
        if summary_elem:
            data['简介'] = await summary_elem.get_attribute('content')
        # 正文内容（仅xxgk-wzcon或xxgk_contnetleft区域）
        content_elem = await page.query_selector('.xxgk-wzcon, .xxgk_contnetleft')
        if content_elem:
            data['正文内容'] = (await content_elem.inner_text()).strip()
        # 敏感数字
        if data['正文内容']:
            data['敏感数字'] = ', '.join(re.findall(r"[\d.]+\s*(?:元|%|％|万元|亿元)", data['正文内容']))
        # 附件（Excel超链接，顿号隔开）
        attach_elems = await page.query_selector_all('a[href]')
        attaches = []
        for a in attach_elems:
            href = await a.get_attribute('href')
            if href and any(x in href for x in ['.pdf', '.doc', '.xls', '.zip', '.rar']):
                if href.startswith('http'):
                    full_href = href
                else:
                    full_href = 'https://czt.ah.gov.cn/' + href.lstrip('/')
                name = (await a.inner_text()).strip()
                attaches.append(f'=HYPERLINK("{full_href}", "{name}")')
        data['附件'] = '\n'.join(attaches)
        # 文件名及链接：标题为Excel超链接
        if data['标题']:
            data['文件名及链接'] = f'=HYPERLINK("{url}", "{data["标题"]}")'
        return [data.get(f, '') for f in FIELDS]
    except Exception as e:
        print(f'采集失败: {url}, 错误: {e}')
        return ['' for _ in FIELDS]

async def gather_with_concurrency(n, tasks):
    semaphore = asyncio.Semaphore(n)
    async def sem_task(task):
        async with semaphore:
            return await task
    return await asyncio.gather(*(sem_task(task) for task in tasks))

async def main():
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        await page.goto(LIST_URL)
        detail_urls = await fetch_all_detail_urls(page)
        print(f'共采集到{len(detail_urls)}条详情页URL')
        # 并发抓取详情页
        tasks = []
        for url_title in detail_urls:
            detail_page = await browser.new_page()
            tasks.append(extract_detail(detail_page, url_title))
        results = await gather_with_concurrency(CONCURRENCY, tasks)
        # 用openpyxl写xlsx，超链接处理
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.append(FIELDS)
        # 设置表头加粗、加边框
        bold_font = Font(bold=True)
        thin_border = Border(left=Side(style='thin'), right=Side(style='thin'), top=Side(style='thin'), bottom=Side(style='thin'))
        for col in range(1, len(FIELDS) + 1):
            cell = ws.cell(row=1, column=col)
            cell.font = bold_font
            cell.border = thin_border
        ws.freeze_panes = 'A2'
        ws.auto_filter.ref = ws.dimensions
        for row in results:
            max_attach = 1
            if row[FIELDS.index('附件')]:
                attachs = row[FIELDS.index('附件')].split('\n')
                max_attach = len(attachs)
            else:
                attachs = ['']
            for i in range(max_attach):
                excel_row = []
                for idx, f in enumerate(FIELDS):
                    if f == '附件':
                        val = attachs[i] if i < len(attachs) else ''
                        excel_row.append(val)
                    else:
                        excel_row.append(row[idx] if i == 0 else '')
                ws.append(excel_row)
            # 附件超链接
            for i, att in enumerate(attachs):
                if att.startswith('=HYPERLINK('):
                    url_title = att[len('=HYPERLINK('):-1]
                    url, title = url_title.split(', "')
                    url = url.strip('"')
                    title = title.strip('"')
                    cell = ws.cell(row=ws.max_row - len(attachs) + 1 + i, column=FIELDS.index('附件') + 1)
                    cell.value = title
                    cell.hyperlink = url
                    cell.style = 'Hyperlink'
            # 文件名及链接超链接
            if row[FIELDS.index('文件名及链接')].startswith('=HYPERLINK('):
                url_title = row[FIELDS.index('文件名及链接')][len('=HYPERLINK('):-1]
                url, title = url_title.split(', "')
                url = url.strip('"')
                title = title.strip('"')
                cell = ws.cell(row=ws.max_row - len(attachs) + 1, column=FIELDS.index('文件名及链接') + 1)
                cell.value = title
                cell.hyperlink = url
                cell.style = 'Hyperlink'
        wb.save('anhui_czt_6715461.xlsx')
        print('采集完成，已输出到anhui_czt_6715461.xlsx')
        await browser.close()

if __name__ == '__main__':
    asyncio.run(main()) 