import subprocess
import time

class ScreenRecorder:
    def __init__(self, ffmpeg_path, output_file='record_demo.mp4', framerate=10, resolution='1280x720', crf=28, preset='veryfast'):
        self.ffmpeg_path = ffmpeg_path
        self.output_file = output_file
        self.framerate = framerate
        self.resolution = resolution
        self.crf = crf
        self.preset = preset
        self.process = None

    def start_recording(self):
        command = f'"{self.ffmpeg_path}" -y -f gdigrab -framerate {self.framerate} -video_size {self.resolution} -i desktop -vcodec libx264 -crf {self.crf} -preset {self.preset} -pix_fmt yuv420p "{self.output_file}"'
        self.process = subprocess.Popen(command, shell=True, stdin=subprocess.PIPE)
        print('开始录屏...')

    def stop_recording(self):
        if self.process:
            self.process.stdin.write('q\n'.encode('GBK'))
            self.process.stdin.flush()
            self.process.communicate()
            print(f'录屏结束，文件已保存为 {self.output_file}')
            self.process = None

if __name__ == '__main__':
    ffmpeg_path = r'E:\software\ffmpeg-7.1.1-essentials_build\bin\ffmpeg.exe'  # 替换为你的实际路径
    recorder = ScreenRecorder(ffmpeg_path=ffmpeg_path)
    recorder.start_recording()
    time.sleep(10)  # 录制10秒
    recorder.stop_recording() 