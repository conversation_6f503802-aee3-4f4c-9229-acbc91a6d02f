# 四川省政务服务网惠企政策爬取脚本
# API地址: https://hqpt.sczwfw.gov.cn/enterprise/gateway/portal/policybasicinformation/page
# 详情页: https://hqpt.sczwfw.gov.cn/enterprise/policyLibraryDetail?id={id}

import re
import requests
from bs4 import BeautifulSoup
import pandas as pd
import datetime
from scripts.fgw.llm_utils import call_llm
from global_logger import get_logger
logger = get_logger(__name__, log_path='scripts/logs/sichuan.log')
from tqdm import tqdm
import os
import time
import json
import openpyxl

# API配置
BASE_URL = "https://hqpt.sczwfw.gov.cn"
LIST_API = f"{BASE_URL}/enterprise/gateway/portal/policybasicinformation/page"
DETAIL_URL_TEMPLATE = f"{BASE_URL}/enterprise/policyLibraryDetail?id={{}}"
ELUCIDATION_API = f"{BASE_URL}/enterprise/gateway/portal/policyElucidation/policyElucidationPage"
INTER_DETAIL_URL_TEMPLATE = f"{BASE_URL}/enterprise/interDetail?id={{}}"

headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Accept": "application/json, text/plain, */*",
    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
    "Content-Type": "application/json",
    "Referer": "https://hqpt.sczwfw.gov.cn/"
}

FIELDS = [
    "来源站点", "部门", "级别", "标题", "简介", "发布时间", "截止时间", "发文机关", "主题", "索引号", "文号", "详情地址", "正文内容", "敏感数字（元，%）", "附件", "文件名及链接"
]

def fetch_policy_list(page=1, page_size=10):
    """获取政策列表"""
    payload = {
        "page": {
            "current": page,
            "size": page_size,
            "total": 0
        },
        "policyListing": "1",
        "areaType": "",
        "siteAreaCode": "",
        "declareType": "",
        "sortType": "1"
    }
    
    try:
        response = requests.post(LIST_API, json=payload, headers=headers, timeout=10)
        response.encoding = 'utf-8'
        
        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 0:
                return data.get('data', {})
        
        return None
    except Exception as e:
        logger.error(f"获取政策列表失败: {e}")
        return None

def fetch_policy_detail(policy_id, title="", debug=False, max_retries=3):
    """获取政策详情"""
    detail_url = DETAIL_URL_TEMPLATE.format(policy_id)
    for attempt in range(max_retries):
        try:
            response = requests.get(detail_url, headers=headers, timeout=15)
            response.encoding = 'utf-8'
            if debug:
                logger.debug(f"    调试信息 - 详情页URL: {detail_url}")
                logger.debug(f"    调试信息 - 响应状态: {response.status_code}")
                logger.debug(f"    调试信息 - 页面长度: {len(response.text)} 字符")
            if response.status_code != 200:
                if attempt < max_retries - 1:
                    time.sleep(2)
                    continue
                return None
            soup = BeautifulSoup(response.text, "html.parser")
            # 提取政策名称
            policy_name = ""
            name_elem = soup.select_one(".policyName")
            if name_elem:
                policy_name = name_elem.get_text(strip=True)
            # 提取发布单位
            issuing_agency = ""
            unit_elem = soup.select_one(".dw")
            if unit_elem:
                issuing_agency = unit_elem.get_text(strip=True).replace("发布单位：", "")
            # 提取发布时间
            pub_date = ""
            time_elem = soup.select_one(".time")
            if time_elem:
                pub_date = time_elem.get_text(strip=True).replace("发布时间：", "")
            # 提取浏览量
            views = ""
            view_elem = soup.select_one(".num")
            if view_elem:
                views = view_elem.get_text(strip=True).replace("浏览量：", "")
            # 提取正文内容
            content = ""
            content_elem = soup.select_one(".ql-editor")
            if content_elem:
                content = content_elem.get_text("\n", strip=True)
            # 提取附件信息
            attachments = []
            file_elems = soup.select(".files li")
            for file_elem in file_elems:
                file_text = file_elem.get_text(strip=True)
                if file_text:
                    attachments.append(file_text)
            attachment_str = "; ".join(attachments) if attachments else ""
            # 提取敏感数字（金额、百分比等）
            sensitive_numbers = []
            if content:
                money_patterns = re.findall(r'\d+(?:\.\d+)?(?:万|千万|亿)?元', content)
                sensitive_numbers.extend(money_patterns)
                percent_patterns = re.findall(r'\d+(?:\.\d+)?%', content)
                sensitive_numbers.extend(percent_patterns)
                other_patterns = re.findall(r'\d+(?:\.\d+)?(?:万|千|百)?(?:人|家|项|个|次|年|月|日)', content)
                sensitive_numbers.extend(other_patterns)
            sensitive = ", ".join(list(set(sensitive_numbers))[:10])
            if debug:
                logger.debug(f"    调试信息 - 提取结果:")
                logger.debug(f"      政策名称: {policy_name[:50]}...")
                logger.debug(f"      发布单位: {issuing_agency}")
                logger.debug(f"      发布时间: {pub_date}")
                logger.debug(f"      浏览量: {views}")
                logger.debug(f"      正文长度: {len(content)} 字符")
                logger.debug(f"      附件数量: {len(attachments)}")
                logger.debug(f"      敏感数字: {sensitive[:100]}..." if sensitive else "      敏感数字: 无")
            return {
                'policy_name': policy_name,
                'issuing_agency': issuing_agency,
                'pub_date': pub_date,
                'views': views,
                'content': content,
                'attachments': attachment_str,
                'sensitive': sensitive,
                'detail_url': detail_url
            }
        except Exception as e:
            if debug:
                logger.debug(f"    调试信息 - 获取详情失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(2)
                continue
            return None
    return None

def fetch_all_policies(max_pages=50):
    """获取所有政策数据"""
    all_data = []
    page = 1
    logger.info(f"开始获取四川省惠企政策数据...")
    while page <= max_pages:
        logger.info(f"正在获取第 {page} 页...")
        list_data = fetch_policy_list(page, 10)
        if not list_data:
            logger.warning(f"第 {page} 页获取失败，停止爬取")
            break
        records = list_data.get('records', [])
        total = list_data.get('total', 0)
        pages = list_data.get('pages', 0)
        if not records:
            logger.warning(f"第 {page} 页无数据，停止爬取")
            break
        logger.info(f"第 {page} 页获取到 {len(records)} 条政策，总计 {total} 条")
        for i, record in enumerate(records, 1):
            policy_id = record.get('id', '')
            policy_name = record.get('policyName', '')
            if not policy_id:
                continue
            logger.info(f"  [{i}/{len(records)}] 正在获取详情: {policy_name[:50]}...")
            debug_mode = (page == 1 and i == 1)
            detail = fetch_policy_detail(policy_id, policy_name, debug=debug_mode)
            if detail:
                # 级别字段写死为邛崃
                level = '邛崃'
                # 敏感数字（元，%）
                content = detail.get('content', '')
                sensitive = ''
                if content:
                    sensitive = ", ".join(re.findall(r"[\d.]+\s*(?:元|%|％)", content))
                # 附件
                attachments = detail.get('attachments', '')
                # 文件名及链接（标题，超链接为详情地址）
                file_link = policy_name
                logger.info(f"采集详情页: 标题={policy_name} 链接={detail.get('detail_url', '')} 正文内容={content[:100]}...")
                # 组装德州字段顺序，来源站点、部门、级别写死
                row = [
                    "四川省人民政府",  # 来源站点（写死）
                    "发改委",        # 部门（写死）
                    level,           # 级别（写死为邛崃）
                    policy_name,     # 标题
                    '',              # 简介（后续生成）
                    record.get('releaseDate', '').split(' ')[0] if record.get('releaseDate') else '',  # 发布时间
                    record.get('policyExecuteEndDate', '').split(' ')[0] if record.get('policyExecuteEndDate') else '',  # 截止时间
                    ', '.join(record.get('responsibleDepartmentNames', [])),  # 发文机关
                    '',  # 主题
                    '',  # 索引号
                    record.get('lssuedCode', ''),  # 文号
                    detail.get('detail_url', ''),  # 详情地址
                    content,  # 正文内容
                    sensitive,  # 敏感数字（元，%）
                    attachments,  # 附件
                    file_link  # 文件名及链接
                ]
                all_data.append(row)
            time.sleep(0.5)
        if page >= int(pages):
            logger.info(f"已获取所有 {pages} 页数据")
            break
        page += 1
        time.sleep(1)
    return all_data

def fetch_elucidation_detail(policy_id, debug=False, max_retries=3):
    """获取政策解读详情（通过POST接口获取JSON）"""
    url = f"{BASE_URL}/enterprise/gateway/portal/policyElucidation/getById/{policy_id}"
    payload = {"id": policy_id}
    for attempt in range(max_retries):
        try:
            response = requests.post(url, json=payload, headers=headers, timeout=10)
            if debug:
                logger.debug(f"调试信息 - 解读详情接口: {url}")
                logger.debug(f"调试信息 - 响应状态: {response.status_code}")
                logger.debug(f"调试信息 - 返回内容: {response.text[:500]}")
            if response.status_code != 200:
                if attempt < max_retries - 1:
                    time.sleep(2)
                    continue
                return None
            data = response.json()
            if data.get("code") != 0 or not data.get("data"):
                return None
            d = data["data"]
            return {
                "policy_name": d.get("elucidationName", ""),
                "issuing_agency": d.get("issueUnit", ""),
                "pub_date": d.get("issueTime", ""),
                "views": d.get("visitsNum", d.get("pageView", "")),
                "content": d.get("remark", ""),  # 如无正文可用remark或留空
                "attachments": "",
                "sensitive": "",
                "detail_url": url
            }
        except Exception as e:
            if debug:
                logger.debug(f"调试信息 - 获取解读详情失败: {e}")
            if attempt < max_retries - 1:
                time.sleep(2)
                continue
            return None
    return None

def save_elucidation_to_excel(data):
    """保存政策解读数据到Excel，文件名及链接列加超链接"""
    if not data:
        logger.warning("没有政策解读数据需要保存")
        return
    import openpyxl
    columns = FIELDS
    df = pd.DataFrame(data, columns=columns)
    folder = "data"
    if not os.path.exists(folder):
        os.makedirs(folder)
    py_name = os.path.splitext(os.path.basename(__file__))[0]
    filename = os.path.join(folder, f"{py_name}.xlsx")
    df.to_excel(filename, index=False, engine='openpyxl')
    wb = openpyxl.load_workbook(filename)
    ws = wb.active
    for idx, row in enumerate(data, start=2):
        title = row[3]
        url = row[11]
        cell = ws.cell(row=idx, column=16)
        cell.value = title
        cell.hyperlink = url
        cell.style = 'Hyperlink'
    wb.save(filename)
    logger.info(f"\n政策解读数据已保存到: {filename}")
    logger.info(f"共保存 {len(data)} 条政策解读数据")

def fetch_policy_elucidation_list(max_pages=50):
    """获取政策解读数据（分开导出）"""
    all_data = []
    page = 1
    while page <= max_pages:
        logger.info(f"正在获取政策解读第 {page} 页...")
        payload = {
            "page": {
                "current": page,
                "size": 10,
                "total": 0,
                "orders": [
                    {"column": "CREATE_TIME", "asc": False}
                ]
            },
            "areaType": "",
            "siteAreaCode": "",
            "order": "1",
            "elucidationType": "1",
            "policyLevels": [],
            "policyCategoryList": [],
            "policyTypeList": []
        }
        try:
            response = requests.post(ELUCIDATION_API, json=payload, headers=headers, timeout=10)
            response.encoding = 'utf-8'
            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 0:
                    list_data = data.get('data', {})
                    records = list_data.get('records', [])
                    pages = list_data.get('pages', 0)
                    if not records:
                        logger.warning(f"政策解读第 {page} 页无数据，停止爬取")
                        break
                    logger.info(f"政策解读第 {page} 页获取到 {len(records)} 条")
                    for i, record in enumerate(records, 1):
                        policy_id = record.get('id', '')
                        policy_name = record.get('title', '')
                        if not policy_id:
                            continue
                        logger.info(f"  [{i}/{len(records)}] 正在获取解读详情: {policy_name[:50]}...")
                        debug_mode = (page == 1 and i == 1)
                        detail = fetch_elucidation_detail(policy_id, policy_name, debug=debug_mode)
                        if detail:
                            level = '邛崃'
                            content = detail.get('content', '')
                            sensitive = ''
                            if content:
                                sensitive = ", ".join(re.findall(r"[\d.]+\s*(?:元|%|％)", content))
                            attachments = detail.get('attachments', '')
                            file_link = policy_name
                            row = [
                                "四川省人民政府",  # 来源站点（写死）
                                "发改委",        # 部门（写死）
                                level,           # 级别（写死为邛崃）
                                policy_name,     # 标题
                                '',              # 简介（后续生成）
                                detail.get('pub_date', ''),  # 发布时间
                                '',  # 截止时间（解读无）
                                detail.get('issuing_agency', ''),  # 发文机关
                                '',  # 主题
                                '',  # 索引号
                                '',  # 文号
                                detail.get('detail_url', ''),  # 详情地址
                                content,  # 正文内容
                                sensitive,  # 敏感数字（元，%）
                                attachments,  # 附件
                                file_link  # 文件名及链接
                            ]
                            all_data.append(row)
                        time.sleep(0.5)
                    if page >= int(pages):
                        logger.info(f"政策解读已获取所有 {pages} 页数据")
                        break
                    page += 1
                    time.sleep(1)
                else:
                    logger.warning(f"政策解读接口返回异常: {data}")
                    break
            else:
                logger.warning(f"政策解读接口HTTP错误: {response.status_code}")
                break
        except Exception as e:
            logger.error(f"获取政策解读失败: {e}")
            break
    return all_data

def generate_summaries(data):
    """生成政策简介"""
    logger.info("\n开始生成政策简介...")
    for i, row in enumerate(tqdm(data, desc="生成简介")):
        content = row[12]
        if content and len(content) > 100:
            try:
                prompt = f"""
请为以下政策生成一个简洁的简介（100字以内）：\n政策内容：{content[:2000]}\n要求：1. 简介要突出政策的核心内容和主要目标 2. 语言简洁明了，100字以内 3. 不要包含具体的联系方式和网址"""
                summary = call_llm(prompt)
                logger.info(f"生成简介: {summary}")
                if summary:
                    row[4] = summary.strip()
                    logger.info(f"已生成第 {i+1} 条简介")
                else:
                    row[4] = ""
            except Exception as e:
                logger.error(f"生成第 {i+1} 条简介失败: {e}")
                row[4] = ""
        else:
            row[4] = ""
        time.sleep(0.1)
    return data

def check_data_completeness(data):
    """检查数据完整性"""
    if not data:
        return

    logger.info("\n=== 数据完整性检查 ===")
    total = len(data)

    # 检查各字段的完整性
    fields = ['政策名称', '文号', '发布时间', '发布单位', '正文内容', '简介']
    field_indices = [0, 1, 2, 3, 9, 4]

    for field, index in zip(fields, field_indices):
        empty_count = sum(1 for row in data if not row[index] or str(row[index]).strip() == '')
        complete_rate = (total - empty_count) / total * 100
        logger.info(f"{field}: {total - empty_count}/{total} ({complete_rate:.1f}%)")

    # 统计发布单位
    units = {}
    for row in data:
        unit = row[3]
        if unit:
            units[unit] = units.get(unit, 0) + 1

    logger.info(f"\n发布单位统计 (前10):")
    for unit, count in sorted(units.items(), key=lambda x: x[1], reverse=True)[:10]:
        logger.info(f"  {unit}: {count}条")

    # 统计地区分布
    areas = {}
    for row in data:
        province = row[4] or "未知"
        city = row[5] or "省级"
        area_key = f"{province}-{city}"
        areas[area_key] = areas.get(area_key, 0) + 1

    logger.info(f"\n地区分布统计 (前10):")
    for area, count in sorted(areas.items(), key=lambda x: x[1], reverse=True)[:10]:
        logger.info(f"  {area}: {count}条")

def save_to_excel(data):
    """保存数据到Excel，文件名及链接列加超链接"""
    if not data:
        logger.warning("没有数据需要保存")
        return
    columns = FIELDS
    df = pd.DataFrame(data, columns=columns)
    folder = "data"
    if not os.path.exists(folder):
        os.makedirs(folder)
    py_name = os.path.splitext(os.path.basename(__file__))[0]
    filename = os.path.join(folder, f"{py_name}.xlsx")
    # 先保存
    df.to_excel(filename, index=False, engine='openpyxl')
    # 再加超链接
    wb = openpyxl.load_workbook(filename)
    ws = wb.active
    for idx, row in enumerate(data, start=2):  # Excel行从2开始
        title = row[3]
        url = row[11]
        cell = ws.cell(row=idx, column=16)  # 第16列是文件名及链接
        cell.value = title
        cell.hyperlink = url
        cell.style = 'Hyperlink'
    wb.save(filename)
    logger.info(f"\n数据已保存到: {filename}")
    logger.info(f"共保存 {len(data)} 条政策数据")
    check_data_completeness(data)

# 主流程分开导出

def main():
    try:
        # 获取惠企政策数据
        data1 = fetch_all_policies(max_pages=2)
        if data1:
            logger.info(f"\n成功获取 {len(data1)} 条惠企政策数据")
            data1 = generate_summaries(data1)
            save_to_excel(data1)
        else:
            logger.warning("未获取到任何惠企政策数据")
        # 获取政策解读数据
        data2 = fetch_policy_elucidation_list(max_pages=2)
        if data2:
            logger.info(f"\n成功获取 {len(data2)} 条政策解读数据")
            data2 = generate_summaries(data2)
            save_elucidation_to_excel(data2)
        else:
            logger.warning("未获取到任何政策解读数据")
    except Exception as e:
        logger.error(f"发生错误: {e}")
        import traceback
        traceback.print_exc()
if __name__ == "__main__":
    main()