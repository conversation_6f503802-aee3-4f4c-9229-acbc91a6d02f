import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin
import time
import pandas as pd
import datetime
import os
import re
from scripts.fgw.llm_utils import call_llm
from global_logger import get_logger
logger = get_logger(__name__, log_path='scripts/logs/上海市发展和改革委员会-上海-公开政务数据-爬取任务.log')
from tqdm import tqdm

BASE_URL = "https://fgw.sh.gov.cn/fgw_zxxxgk/index.html"
HOST = "https://fgw.sh.gov.cn"
HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}

FIELDS = [
    "来源站点", "部门", "级别", "标题", "简介", "发布时间", "截止时间", "发文机关", "主题", "索引号", "文号", "详情地址", "正文内容", "敏感数字（元，%）", "附件", "文件名及链接"
]

def get_page_url(page_num):
    if page_num == 1:
        return BASE_URL
    return f"https://fgw.sh.gov.cn/fgw_zxxxgk/index_{page_num}.html"

def fetch_list_page(url):
    resp = requests.get(url, headers=HEADERS, timeout=10)
    resp.encoding = resp.apparent_encoding
    soup = BeautifulSoup(resp.text, "html.parser")
    news_list = soup.select("ul.uli14.news-list li")
    items = []
    for li in news_list:
        a = li.find("a")
        date_span = li.find("span", class_="time")
        if not a or not date_span:
            continue
        href = urljoin(HOST, a.get("href"))
        title = a.get("title") or a.text.strip()
        date = date_span.text.strip()
        items.append({"title": title, "href": href, "date": date})
    return items

def fetch_detail(url, title, pub_date):
    resp = requests.get(url, headers=HEADERS, timeout=10)
    resp.encoding = resp.apparent_encoding
    soup = BeautifulSoup(resp.text, "html.parser")
    # 正文内容
    content_div = soup.find("div", id="ivs_content")
    content = content_div.get_text("\n", strip=True) if content_div else ""
    # 文号
    doc_no = ""
    h2 = soup.find("h2", class_="Article-title")
    if h2:
        small = h2.find("small", class_="Article-time")
        if small:
            doc_no_match = re.search(r"文号：([^\)\s]+)", small.get_text())
            if doc_no_match:
                doc_no = doc_no_match.group(1)
    # 敏感数字（元，%）
    sensitive = ", ".join(re.findall(r"[\d.]+\s*(?:元|%|％)", content))
    # 附件
    attachment = ""
    file_links = []
    xgfj = soup.find("div", class_="xgfj")
    if xgfj:
        attach_links = xgfj.find_all("a", href=True)
        attach_urls = [urljoin(HOST, a['href']) for a in attach_links]
        attachment = ", ".join(attach_urls)
        file_links = [f"{a.get('title') or a.text.strip()}" for a in attach_links]
    # 文件名及链接
    file_link = title
    # 组装字段
    row = [
        "上海市发展和改革委员会",  # 来源站点
        "发改委",                # 部门
        "上海",                  # 级别
        title,                  # 标题
        "",                     # 简介
        pub_date,               # 发布时间
        "",                     # 截止时间
        "",                     # 发文机关
        "",                     # 主题
        "",                     # 索引号
        doc_no,                 # 文号
        url,                    # 详情地址
        content,                # 正文内容
        sensitive,              # 敏感数字
        attachment,             # 附件
        file_link               # 文件名及链接
    ]
    return row

def save_to_excel(data, filename=None):
    data_dir = os.path.join(os.path.dirname(__file__), "data")
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)
    py_name = os.path.splitext(os.path.basename(__file__))[0]
    filename = f"{py_name}.xlsx"
    filepath = os.path.join(data_dir, filename)
    df = pd.DataFrame(data, columns=FIELDS)
    writer = pd.ExcelWriter(filepath, engine='openpyxl')
    df.to_excel(writer, index=False)
    worksheet = writer.sheets['Sheet1']
    for idx, row in enumerate(data, start=2):
        title = row[3]
        url = row[11]
        cell = worksheet.cell(row=idx, column=16)
        cell.value = title
        cell.hyperlink = url
        cell.style = 'Hyperlink'
    writer.close()
    logger.info(f"已保存到 {filepath}")

def main():
    page = 1
    found_2025 = False
    all_data = []
    while True:
        url = get_page_url(page)
        logger.info(f"抓取列表页: {url}")
        items = fetch_list_page(url)
        if not items:
            logger.info("未找到新闻列表，终止。")
            break
        for item in items:
            year = item["date"].split("-")[0]
            if year == "2025":
                found_2025 = True
                logger.info(f"[2025] {item['date']} {item['title']} {item['href']}")
                row = fetch_detail(item["href"], item["title"], item["date"])
                logger.info(f"采集详情页: 标题={row[3]} 链接={item['href']} 正文内容={row[12][:100]}...")
                all_data.append(row)
                logger.info(f"正文摘要: {row[12][:100]}...\n")
                time.sleep(0.5)
            else:
                logger.info(f"遇到非2025年新闻: {item['date']} {item['title']}，终止后续抓取。")
                # 生成简介
                for row in tqdm(all_data, desc="生成简介", ncols=80):
                    content = row[12] if len(row) > 12 else ""
                    summary = call_llm(content) if content else ""
                    logger.info(f"生成简介: {summary}")
                    row[4] = summary
                save_to_excel(all_data)
                return
        page += 1
        time.sleep(1)
    if found_2025:
        for row in tqdm(all_data, desc="生成简介", ncols=80):
            content = row[12] if len(row) > 12 else ""
            summary = call_llm(content) if content else ""
            logger.info(f"生成简介: {summary}")
            row[4] = summary
        save_to_excel(all_data)
    else:
        logger.info("未找到2025年新闻。")

if __name__ == "__main__":
    main()
