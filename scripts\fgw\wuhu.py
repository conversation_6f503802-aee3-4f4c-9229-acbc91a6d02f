import requests
from bs4 import BeautifulSoup
import pandas as pd
import os
import time
import re
from tqdm import tqdm
from scripts.fgw.llm_utils import call_llm
from global_logger import get_logger
logger = get_logger(__name__, log_path='scripts/logs/安徽省发展和改革委员会-芜湖-公开政务数据-爬取任务.log')

BASE_URLS = [
    'https://fzggw.ah.gov.cn/jgsz/jgcs/myjjj/hqzc/index.html',
    'https://fzggw.ah.gov.cn/content/column/51543991?pageIndex=2',
    'https://fzggw.ah.gov.cn/content/column/51543991?pageIndex=3',
]

HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
}

FIELDS = [
    "来源站点", "部门", "级别", "标题", "简介", "发布时间", "截止时间", "发文机关", "主题", "索引号", "文号", "详情地址", "正文内容", "敏感数字（元，%）", "附件", "文件名及链接"
]

# 硬编码字段
SOURCE_SITE = '安徽省发展和改革委员会'
DEPARTMENT = '发改委'
LEVEL = '芜湖'


def get_policy_list():
    policies = []
    for url in BASE_URLS:
        resp = requests.get(url, headers=HEADERS, timeout=15)
        resp.encoding = resp.apparent_encoding
        soup = BeautifulSoup(resp.text, 'html.parser')
        ul = soup.find('ul', class_='doc_list')
        if not ul:
            continue
        for li in ul.find_all('li'):
            a = li.find('a')
            date_span = li.find('span', class_='date')
            if a and date_span:
                link = a['href'] if a['href'].startswith('http') else 'https://fzggw.ah.gov.cn' + a['href']
                title = a.get('title') or a.get_text(strip=True)
                date = date_span.get_text(strip=True)
                policies.append({'title': title, 'url': link, 'date': date})
    return policies


def fetch_policy_detail(url):
    try:
        resp = requests.get(url, headers=HEADERS, timeout=15)
        resp.encoding = resp.apparent_encoding
        soup = BeautifulSoup(resp.text, 'html.parser')
        # 标题
        title_tag = soup.find('h1', class_='ls-article-title')
        title = title_tag.get_text(separator='', strip=True) if title_tag else ''
        # 发布日期
        pub_date = ''
        source = ''
        doc_no = ''
        menu_div = soup.find('div', class_='ls-article-menu')
        if menu_div:
            spans = menu_div.find_all('span', class_='sp')
            for sp in spans:
                txt = sp.get_text(strip=True)
                if '发布日期' in txt:
                    pub_date = txt.replace('发布日期：', '').split(' ')[0]
                elif '来源' in txt:
                    source = txt.replace('来源：', '')
        # 文号优先取ls-article-info的第一个p标签
        info_div = soup.find('div', class_='ls-article-info')
        if info_div:
            p_list = info_div.find_all('p')
            if p_list:
                first_p = p_list[0].get_text(strip=True)
                if '号' in first_p:
                    doc_no = first_p
        # 若未取到文号，再尝试原有subtitle  
        if not doc_no:
            subtitle = soup.find('div', class_='ItemDetailRed-header-subtitle')
            if subtitle:
                doc_no = subtitle.get_text(strip=True)
        # 正文内容优先wenzhang-content
        content = ''
        attachment = ''
        content_div = soup.find('div', id='wenzhang-content')
        if content_div:
            content = content_div.get_text('\n', strip=True)
            attach_links = content_div.find_all('a', href=True)
            attach_urls = [a['href'] if a['href'].startswith('http') else 'https://fzggw.ah.gov.cn' + a['href'] for a in attach_links]
            attachment = ', '.join(attach_urls)
        else:
            # 兜底：ls-article-info下所有p拼接（去掉文号行）
            if info_div:
                p_list = info_div.find_all('p')
                ps = []
                for idx, p in enumerate(p_list):
                    txt = p.get_text(strip=True)
                    # 跳过文号行
                    if idx == 0 and '号' in txt:
                        continue
                    ps.append(txt)
                content = '\n'.join(ps)
                # 附件
                attach_links = info_div.find_all('a', href=True)
                attach_urls = [a['href'] if a['href'].startswith('http') else 'https://fzggw.ah.gov.cn' + a['href'] for a in attach_links]
                attachment = ', '.join(attach_urls)
            # 再兜底：info_div全部文本
            if not content and info_div:
                content = info_div.get_text('\n', strip=True)
        # 敏感数字
        sensitive = ', '.join(re.findall(r"[\d.]+\s*(?:元|%|％)", content))
        return {
            'title': title,
            'pub_date': pub_date,
            'doc_no': doc_no,
            'content': content,
            'attachment': attachment,
            'sensitive': sensitive,
        }
    except Exception as e:
        logger.error(f"Error fetching {url}: {e}")
        return {
            'title': '',
            'pub_date': '',
            'doc_no': '',
            'content': '',
            'attachment': '',
            'sensitive': '',
        }


def main():
    policies = get_policy_list()
    data = []
    # 先采集所有详情
    details = []
    for item in tqdm(policies, desc='采集详情页'):
        detail = fetch_policy_detail(item['url'])
        logger.info(f"采集详情页: 标题={item['title']} 链接={item['url']} 正文内容={detail['content'][:100]}...")
        details.append((item, detail))
    # 生成简介
    for item, detail in tqdm(details, desc='生成简介'):
        summary = ''
        if detail['content']:
            summary = call_llm(detail['content'])
            logger.info(f"生成简介: {summary}")
        row = [
            SOURCE_SITE,  # 来源站点
            DEPARTMENT,   # 部门
            LEVEL,        # 级别
            detail['title'] or item['title'],  # 标题
            summary,      # 简介
            detail['pub_date'] or item['date'],  # 发布时间
            '',           # 截止时间
            '',           # 发文机关
            '',           # 主题
            '',           # 索引号
            detail['doc_no'],  # 文号
            item['url'],  # 详情地址
            detail['content'],  # 正文内容
            detail['sensitive'],  # 敏感数字
            detail['attachment'],  # 附件
            detail['title'] or item['title'],  # 文件名及链接（Excel中加超链接）
        ]
        data.append(row)
    # 创建data文件夹
    os.makedirs('data', exist_ok=True)
    py_name = os.path.splitext(os.path.basename(__file__))[0]
    filename = f'scripts/fgw/data/{py_name}.xlsx'
    df = pd.DataFrame(data, columns=FIELDS)
    # 添加超链接
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        df.to_excel(writer, index=False)
        ws = writer.sheets['Sheet1']
        for idx, row in enumerate(data, start=2):
            cell = ws.cell(row=idx, column=16)  # 第16列
            cell.value = row[3]  # 标题
            cell.hyperlink = row[11]  # 详情地址
            cell.style = 'Hyperlink'
    logger.info(f'导出成功: {filename}')


if __name__ == '__main__':
    main()
