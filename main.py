from fastapi import FastAP<PERSON>, HTTPException, Body, Query, BackgroundTasks
from fastapi import Request
from pydantic import BaseModel
from typing import List, Optional
import pymysql
from db_conn import config
import uuid
from croniter import croniter
from datetime import datetime
import os
import subprocess
import importlib
import io
import contextlib
import sys
import pandas as pd
import math
import time
import random
from global_logger import get_logger
logger = get_logger(__name__)


app = FastAPI()

# Pydantic 模型
class Task(BaseModel):
    taskName: Optional[str]=None
    script: Optional[str]=None
    cronTab: Optional[str]=None
    status: Optional[int] = None
    nextRunTime: Optional[str]=None
    lastRunTime: Optional[str]=None
    runTime: Optional[str]=None

class TaskInDB(Task):
    id: str

# 获取数据库连接
def get_conn():
    return pymysql.connect(**config)

# 新增任务
@app.post('/tasks')
def create_task(task: Task):
    conn = get_conn()
    try:
        with conn.cursor() as cursor:
            id = str(uuid.uuid4())
            # 计算下次运行时间
            next_run_time = croniter(task.cronTab, datetime.now()).get_next(datetime)
            # 格式化为字符串（如 '2024-06-08 12:00:00'）
            next_run_time_str = next_run_time.strftime('%Y-%m-%d %H:%M:%S')
            sql = "INSERT INTO ptl_task_config (ID, TASK_NAME, SCRIPT, CRON_TAB, NEXT_RUN_TIME) VALUES (%s, %s, %s, %s, %s)"
            cursor.execute(sql, (id, task.taskName, task.script, task.cronTab, next_run_time_str))
            conn.commit()
        return {"status": 200, "msg": "成功", "data": {"id": id, **task.dict(), "next_run_time": next_run_time_str}}
    except Exception as e:
        logger.error(f"新增任务失败: {e}")
        return {"status": 500, "msg": "失败"}
    finally:
        conn.close()



# 查询所有任务
@app.post('/tasks/list')
def read_tasks(query: Task = Body(...)):
    conn = get_conn()
    try:
        with conn.cursor() as cursor:
            params = []
            sql = '''
            select t1.ID,t1.TASK_NAME, t1.SCRIPT, t1.CRON_TAB, t1.NEXT_RUN_TIME, t1.LAST_RUN_TIME, t1.RUN_TIME, t1.STATUS
            from ptl_task_config t1
            where 1=1'''
            if query.taskName:
                sql += " AND t1.TASK_NAME LIKE %s"
                params.append(f"%{query.taskName}%")
                params.append(query.status)
            if query.lastRunTime:
                sql += " AND t1.LAST_RUN_TIME >= %s"
                params.append(query.lastRunTime)
            if query.nextRunTime:
                sql += " AND t1.NEXT_RUN_TIME >= %s"
                params.append(query.nextRunTime)
            if query.status:
                sql += " AND t1.STATUS = %s"
                params.append(query.status)
            cursor.execute(sql, params)
            rows = cursor.fetchall()
            data = [TaskInDB(id=row[0], taskName=row[1], script=row[2], cronTab=row[3], nextRunTime=row[4], lastRunTime=row[5], runTime=row[6],status=row[7]) for row in rows]
            return {"status": 200, "msg": "成功", "data": data}
    except Exception as e:
        logger.error(f"查询失败: {e}")
        return {"status": 500, "msg": "失败"}
    finally:
        conn.close()

# 查询单个任务
@app.get('/tasks/{task_id}')
def read_task(task_id: str):
    conn = get_conn()
    try:
        with conn.cursor() as cursor:
            sql = "SELECT ID, TASK_NAME, SCRIPT, CRON_TAB FROM ptl_task_config WHERE ID=%s"
            cursor.execute(sql, (task_id,))
            row = cursor.fetchone()
            if not row:
                return {"status": 500, "msg": "查询失败"}

            return {"status": 200, "msg": "成功", "data": TaskInDB(id=row[0], taskName=row[1], script=row[2], cronTab=row[3])}
    except Exception as e:
        logger.error(f"查询失败: {e}")
        return {"status": 500, "msg": "失败"}
    finally:
        conn.close()

def safe_value(val):
    if val is None:
        return None
    if isinstance(val, float) and math.isnan(val):
        return None
    return val

def insert_xlsx_to_policy_info(filepath,task_id,task_name):
    df = pd.read_excel(filepath)
    records = df.to_dict(orient='records')
    conn = get_conn()
    try:
        with conn.cursor() as cursor:
            for row in records:
                id = str(uuid.uuid4()).replace('-', '')
                sql = """
                    INSERT INTO ptl_policy_info (
                        ID, SOURCE_SITE, DEPT, LEVEL, TITLE, SUMMARY, PUBLISH_TIME, END_TIME, ISSUING_AGENCY, TOPIC, INDEX_NO, DOC_NO, DETAIL_URL, CONTENT, SENSITIVE_NUMBERS, ATTACHMENT, FILE_LINK, CREATE_TIME,TASK_NAME,TASK_ID
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                cursor.execute(sql, (
                    id,
                    safe_value(row.get('来源站点')),
                    safe_value(row.get('部门')),
                    safe_value(row.get('级别')),
                    safe_value(row.get('标题')),
                    safe_value(row.get('简介')),
                    safe_value(row.get('发布时间')),
                    safe_value(row.get('截止时间')),
                    safe_value(row.get('发文机关')),
                    safe_value(row.get('主题')),
                    safe_value(row.get('索引号')),
                    safe_value(row.get('文号')),
                    safe_value(row.get('详情地址')),
                    safe_value(row.get('正文内容')),
                    safe_value(row.get('数字（元，%）')),
                    safe_value(row.get('附件')),
                    safe_value(row.get('件名及链接')),
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    task_name,
                    task_id
                ))
            conn.commit()
    finally:
        conn.close()

# 在任务状态更新接口后追加调用（以 /tasks/update/{task_id} 为例）
@app.post('/tasks/update/{task_id}')
def update_task(task_id: str, task: Task):
    conn = get_conn()
    try:
        with conn.cursor() as cursor:
            next_run_time = croniter(task.cronTab, datetime.now()).get_next(datetime)
            next_run_time_str = next_run_time.strftime('%Y-%m-%d %H:%M:%S')
            sql = "UPDATE ptl_task_config SET TASK_NAME=%s, SCRIPT=%s, CRON_TAB=%s, NEXT_RUN_TIME=%s WHERE ID=%s"
            cursor.execute(sql, (task.taskName, task.script, task.cronTab, next_run_time_str, task_id))
            conn.commit()
            if cursor.rowcount == 0:
                return {"status": 500, "msg": "失败"}
        return {"status": 200, "msg": "成功", "data": TaskInDB(id=task_id, **task.dict())}
    except Exception as e:
        logger.error(f"更新失败: {e}")
        return {"status": 500, "msg": "失败"}
    finally:
        conn.close()

# 删除任务
@app.get('/tasks/delete/{task_id}')
def delete_task(task_id: str):
    conn = get_conn()
    try:
        with conn.cursor() as cursor:
            sql = "DELETE FROM ptl_task_config WHERE ID=%s"
            cursor.execute(sql, (task_id,))
            conn.commit()
            if cursor.rowcount == 0:
                return {"status": 500, "msg": "失败"}
        return {"status": 200, "msg": "成功"}
    except Exception as e:
        logger.error(f"删除失败: {e}")
        return {"status": 500, "msg": "失败"}
    finally:
        conn.close()

def run_spider_and_insert(script_name, task_id, module_name):
    import importlib
    import io
    import contextlib
    import os
    from global_logger import get_logger
    logger = get_logger(__name__)
    conn = get_conn()
    try:
        start_time=datetime.now()
        
        # 更新任务状态
        with conn.cursor() as cursor:
            sql = "UPDATE ptl_task_config SET STATUS=1 WHERE ID=%s"
            cursor.execute(sql, (task_id,))
            conn.commit()

            del_sql = "DELETE FROM ptl_policy_info WHERE TASK_ID=%s"
            cursor.execute(del_sql, (task_id,))
            conn.commit()
            
        time.sleep(random.randint(10, 15))
            
        # 调用爬虫引擎并入库
        module_path = f"scripts.fgw.{module_name}"
        module = importlib.import_module(module_path)
        if not hasattr(module, "main"):
            logger.error(f"{script_name} 没有 main() 方法")
            return
        f_stdout = io.StringIO()
        f_stderr = io.StringIO()
        logger.info("开始执行脚本")

        start_time=datetime.now()
        with contextlib.redirect_stdout(f_stdout), contextlib.redirect_stderr(f_stderr):
            module.main()
        run_time=datetime.now()-start_time
        with conn.cursor() as cursor:
            sql = "UPDATE ptl_task_config SET STATUS=2, LAST_RUN_TIME=%s, RUN_TIME=%s WHERE ID=%s"
            cursor.execute(sql, (datetime.now().strftime('%Y-%m-%d %H:%M:%S'),run_time,task_id,))
            conn.commit()
        
        xlsx_path = f"scripts/fgw/data/{module_name}.xlsx"
        
        if os.path.exists(xlsx_path):
            insert_xlsx_to_policy_info(xlsx_path, task_id, module_name)
            with conn.cursor() as cursor:
                run_time=datetime.now()-start_time
                sql = "UPDATE ptl_task_config SET STATUS=2, LAST_RUN_TIME=%s, RUN_TIME=%s WHERE ID=%s"
                cursor.execute(sql, (datetime.now().strftime('%Y-%m-%d %H:%M:%S'),run_time,task_id,))
                conn.commit()
        
        else:
            logger.warning(f"未找到对应的 xlsx 文件: {xlsx_path}")
    except Exception as e:
        logger.error(f"后台任务出错: {e}")
    finally:
        conn.close()

@app.post('/run_script')
def run_script(data: dict = Body(...), background_tasks: BackgroundTasks = None):
    script_name = data.get('script')
    if not script_name or not script_name.endswith('.py') or '/' in script_name or '\\' in script_name:
        return {"status": 500, "msg": "失败"}
    module_name = script_name[:-3]
    background_tasks.add_task(run_spider_and_insert, script_name, data.get('id'), module_name)
    return {"status": 200, "msg": "任务已提交，后台执行"}
    

# --- 以下为 ptl_policy_info 增删查改接口 ---
from typing import Optional
import uuid

class PolicyInfo(BaseModel):
    ID: Optional[str]=None
    SOURCE_SITE: Optional[str]=None
    DEPT: Optional[str]=None
    LEVEL: Optional[str]=None
    TITLE: Optional[str]=None
    SUMMARY: Optional[str]=None
    PUBLISH_TIME: Optional[str]=None
    END_TIME: Optional[str]=None
    ISSUING_AGENCY: Optional[str]=None
    TOPIC: Optional[str]=None
    INDEX_NO: Optional[str]=None
    DOC_NO: Optional[str]=None
    DETAIL_URL: Optional[str]=None
    CONTENT: Optional[str]=None
    SENSITIVE_NUMBERS: Optional[str]=None
    ATTACHMENT: Optional[str]=None
    FILE_LINK: Optional[str]=None
    CREATE_TIME: Optional[str]=None

# 启用政策
@app.get('/policy/active/{id}')
def update_policy_status(id: str):
    conn = get_conn()
    try:
        with conn.cursor() as cursor:
            sql = "UPDATE ptl_policy_info SET IS_USE=1 WHERE ID=%s"
            cursor.execute(sql, (id,))
            conn.commit()
            if cursor.rowcount == 0:
                return {"status": 500, "msg": "失败"}
        return {"status": 200, "msg": "成功"}
    except Exception as e:
        logger.error(f"启用失败: {e}")
        return {"status": 500, "msg": "失败"}
    finally:
        conn.close()

# 弃用政策
@app.get('/policy/deactive/{id}')
def update_policy_status(id: str):
    conn = get_conn()
    try:
        with conn.cursor() as cursor:
            sql = "UPDATE ptl_policy_info SET IS_USE=9 WHERE ID=%s"
            cursor.execute(sql, (id,))
            conn.commit()
            if cursor.rowcount == 0:
                return {"status": 500, "msg": "失败"}
        return {"status": 200, "msg": "成功"}
    except Exception as e:
        logger.error(f"弃用失败: {e}")
        return {"status": 500, "msg": "失败"}
    finally:
        conn.close()

class PolicyQuery(BaseModel):
    title: Optional[str] = None
    issuingAgency: Optional[str] = None
    publishTimeStart: Optional[str] = None
    publishTimeEnd: Optional[str] = None
    endTimeStart: Optional[str] = None
    endTimeEnd: Optional[str] = None
    isUse: Optional[int] = None    # 1: 启用，9: 弃用
    pageNum: int = 1
    pageSize: int = 10,
    dept: Optional[str] = None  
    level: Optional[str] = None

class PolicyTaskList(BaseModel):
    id: Optional[str]=None
    taskName: Optional[str]=None
    sourceSite: Optional[str]=None
    dept: Optional[str]=None
    level: Optional[str]=None

@app.post("/policy")
def policy_task_list(query: PolicyTaskList = Body(...)):
    conn = get_conn()
    try:
        with conn.cursor() as cursor:
            params=[]
            sql = '''
            select t1.ID,t1.TASK_NAME, t2.SOURCE_SITE, t2.DEPT, t2.LEVEL 
            from ptl_task_config t1 left join ptl_policy_info t2 on t1.ID=t2.TASK_ID
            where 1=1
            '''
            if query.taskName:
                sql += " AND t1.TASK_NAME LIKE %s"
                params.append(f"%{query.taskName}%")
            if query.dept:
                sql += " AND t2.DEPT LIKE %s"
                params.append(f"%{query.dept}%")

            sql+=' group by t1.TASK_NAME, t2.SOURCE_SITE, t2.DEPT, t2.LEVEL,t1.ID'
            cursor.execute(sql, params)
            logger.info(sql)
            logger.info(params)
            rows = cursor.fetchall()
            data = [PolicyTaskList(id=row[0], taskName=row[1] , sourceSite=row[2], dept=row[3], level=row[4]) for row in rows]
            return {"status": 200, "msg": "成功", "data": data}
    except Exception as e:
        logger.error(f"查询失败: {e}")
        return {"status": 500, "msg": "失败"}
    finally:
        conn.close()



@app.post('/policy/{task_id}')
def list_policy(task_id: str, query: PolicyQuery = Body(...)):
    if task_id == 'all':
        conn = get_conn()
        try:
            with conn.cursor() as cursor:
                params = []
                sql = "SELECT * FROM ptl_policy_info WHERE 1=1"
                count_sql = "SELECT COUNT(*) FROM ptl_policy_info WHERE 1=1"
                if query.title:
                    sql += " AND TITLE LIKE %s"
                    count_sql += " AND TITLE LIKE %s"
                    params.append(f"%{query.title}%")
                if query.issuingAgency:
                    sql += " AND ISSUING_AGENCY LIKE %s"
                    count_sql += " AND ISSUING_AGENCY LIKE %s"
                    params.append(f"%{query.issuingAgency}%")
                if query.publishTimeStart:
                    sql += " AND PUBLISH_TIME >= %s"
                    count_sql += " AND PUBLISH_TIME >= %s"
                    params.append(query.publishTimeStart)
                if query.publishTimeEnd:
                    sql += " AND PUBLISH_TIME <= %s"
                    count_sql += " AND PUBLISH_TIME <= %s"
                    params.append(query.publishTimeEnd)
                if query.endTimeStart:
                    sql += " AND END_TIME >= %s"
                    count_sql += " AND END_TIME >= %s"
                    params.append(query.endTimeStart)
                if query.endTimeEnd:
                    sql += " AND END_TIME <= %s"
                    count_sql += " AND END_TIME <= %s"
                    params.append(query.endTimeEnd)
                if query.isUse:
                    sql += " AND IS_USE = %s"
                    count_sql += " AND IS_USE = %s"
                    params.append(query.isUse)
                if query.dept:
                    sql += " AND DEPT LIKE %s"
                    count_sql += " AND DEPT LIKE %s"
                    params.append(f"%{query.dept}%")
                if query.level:
                    sql += " AND LEVEL LIKE %s"
                    count_sql += " AND LEVEL LIKE %s"   
                    params.append(f"%{query.level}%")
                # 分页
                offset = (query.pageNum - 1) * query.pageSize
                sql += " ORDER BY PUBLISH_TIME DESC LIMIT %s OFFSET %s"
                data_params = params + [query.pageSize, offset]
                # 查询总数
                cursor.execute(count_sql, params)
                total = cursor.fetchone()[0]
                # 查询数据
                cursor.execute(sql, data_params)
                rows = cursor.fetchall()
                columns = [col[0] for col in cursor.description]
                result = [dict(zip(columns, row)) for row in rows]
                return {
                    "status": 200,
                    "msg": "成功",
                    "data": result,
                    "total": total,
                    "pageNum": query.pageNum,
                    "pageSize": query.pageSize
                }
        except Exception as e:
            logger.error(f"查询失败: {e}")
            return {"status": 500, "msg": "失败"}
        finally:
            conn.close()
    else:
        conn = get_conn()
        try:
            with conn.cursor() as cursor:
                params = []
                sql = "SELECT * FROM ptl_policy_info WHERE 1=1 AND TASK_ID = %s"
                count_sql = "SELECT COUNT(*) FROM ptl_policy_info WHERE 1=1 AND TASK_ID = %s"
                params.append(task_id)
                count_params = [task_id]
                if query.title:
                    sql += " AND TITLE LIKE %s"
                    count_sql += " AND TITLE LIKE %s"
                    params.append(f"%{query.title}%")
                    count_params.append(f"%{query.title}%")
                if query.issuingAgency:
                    sql += " AND ISSUING_AGENCY LIKE %s"
                    count_sql += " AND ISSUING_AGENCY LIKE %s"
                    params.append(f"%{query.issuingAgency}%")
                    count_params.append(f"%{query.issuingAgency}%")
                if query.publishTimeStart:
                    sql += " AND PUBLISH_TIME >= %s"
                    count_sql += " AND PUBLISH_TIME >= %s"
                    params.append(query.publishTimeStart)
                    count_params.append(query.publishTimeStart)
                if query.publishTimeEnd:
                    sql += " AND PUBLISH_TIME <= %s"
                    count_sql += " AND PUBLISH_TIME <= %s"
                    params.append(query.publishTimeEnd)
                    count_params.append(query.publishTimeEnd)
                if query.endTimeStart:
                    sql += " AND END_TIME >= %s"
                    count_sql += " AND END_TIME >= %s"
                    params.append(query.endTimeStart)
                    count_params.append(query.endTimeStart)
                if query.endTimeEnd:
                    sql += " AND END_TIME <= %s"
                    count_sql += " AND END_TIME <= %s"
                    params.append(query.endTimeEnd)
                    count_params.append(query.endTimeEnd)
                if query.isUse:
                    sql += " AND IS_USE = %s"
                    count_sql += " AND IS_USE = %s"
                    params.append(query.isUse)
                    count_params.append(query.isUse)
                # 分页
                offset = (query.pageNum - 1) * query.pageSize
                sql += " ORDER BY PUBLISH_TIME DESC LIMIT %s OFFSET %s"
                params.extend([query.pageSize, offset])
                # 查询总数
                cursor.execute(count_sql, count_params)
                total = cursor.fetchone()[0]
                # 查询数据
                cursor.execute(sql, params)
                rows = cursor.fetchall()
                columns = [col[0] for col in cursor.description]
                result = [dict(zip(columns, row)) for row in rows]
                return {
                    "status": 200,
                    "msg": "成功",
                    "data": result,
                    "total": total,
                    "pageNum": query.pageNum,
                    "pageSize": query.pageSize
                }
        except Exception as e:
            logger.error(f"查询失败: {e}")
            return {"status": 500, "msg": "失败"}
        finally:
            conn.close()

# 新增日志文件列表接口
@app.get('/logs')
def list_logs():
    logs_dir = os.path.join('scripts', 'logs')
    try:
        if not os.path.exists(logs_dir):
            return {"status": 200, "data": [], "msg": "logs目录不存在"}
        files = [f for f in os.listdir(logs_dir) if os.path.isfile(os.path.join(logs_dir, f))]
        return {"status": 200, "data": files, "msg": "success"}
    except Exception as e:
        logger.error(f"读取日志文件列表失败: {e}")
        return {"status": 500, "data": [], "msg": str(e)}

# 新增日志内容读取接口
@app.get('/log')
def read_log(filename: str = Query(..., description="日志文件名")):
    logs_dir = os.path.join('scripts', 'logs')
    file_path = os.path.join(logs_dir, filename)
    try:
        if not os.path.exists(file_path):
            return {"status": 404, "data": "", "msg": "日志文件不存在"}
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        return {"status": 200, "data": content, "msg": "success"}
    except Exception as e:
        logger.error(f"读取日志内容失败: {e}")
        return {"status": 500, "data": "", "msg": str(e)}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",   # 指定IP
        port=7865,         # 指定端口
        reload=True        # 启动类型，开发时建议True
    ) 
