import pymysql

# 数据库连接参数
config = {
    'host': '************',      # 数据库主机地址
    'user': 'root',  # 数据库用户名
    'password': 'yunqu168',  # 数据库密码
    'database': 'ptl_demo',  # 数据库名
    'port': 3306,             # 端口号，默认为 3306
    'charset': 'utf8mb4'      # 字符集
}

try:
    # 创建连接
    connection = pymysql.connect(**config)
    print('数据库连接成功！')

    # 创建游标对象
    with connection.cursor() as cursor:
        # 执行 SQL 查询
        sql = 'SELECT VERSION()'
        cursor.execute(sql)
        # 获取结果
        result = cursor.fetchone()
        print('MySQL 版本:', result[0])

except pymysql.MySQLError as e:
    print('数据库连接失败:', e)
finally:
    # 关闭连接
    if 'connection' in locals() and connection.open:
        connection.close()
        print('数据库连接已关闭。') 