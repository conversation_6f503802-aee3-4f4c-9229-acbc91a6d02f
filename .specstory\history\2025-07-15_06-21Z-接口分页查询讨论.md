<!-- Generated by SpecStory -->

# 接口分页查询讨论 (2025-07-15 06:21Z)

_**User**_

这个接口采用 分页查询

---

_**Assistant**_

要为 `/policy/{task_id}` 这个接口添加分页查询功能，你需要：

1. 在 `PolicyQuery` 里增加分页参数（如 pageNum, pageSize）。
2. SQL 查询里加上 LIMIT 和 OFFSET。
3. 返回结果时，带上总条数（total）、当前页、每页数量等分页信息。

下面是详细修改方案：

### 1. 修改 PolicyQuery，增加分页参数

```python
class PolicyQuery(BaseModel):
    title: Optional[str] = None
    issuingAgency: Optional[str] = None
    publishTimeStart: Optional[str] = None
    publishTimeEnd: Optional[str] = None
    endTimeStart: Optional[str] = None
    endTimeEnd: Optional[str] = None
    isUse: Optional[int] = None    # 1: 启用，9: 弃用
    pageNum: int = 1
    pageSize: int = 10
```

### 2. 修改接口实现，支持分页

```python
@app.post('/policy/{task_id}')
def list_policy(task_id: str, query: PolicyQuery = Body(...)):
    conn = get_conn()
    try:
        with conn.cursor() as cursor:
            params = []
            sql = "SELECT * FROM ptl_policy_info WHERE 1=1 AND TASK_ID = %s"
            count_sql = "SELECT COUNT(*) FROM ptl_policy_info WHERE 1=1 AND TASK_ID = %s"
            params.append(task_id)
            count_params = [task_id]
            # 拼接条件
            if query.title:
                sql += " AND TITLE LIKE %s"
                count_sql += " AND TITLE LIKE %s"
                params.append(f"%{query.title}%")
                count_params.append(f"%{query.title}%")
            if query.issuingAgency:
                sql += " AND ISSUING_AGENCY LIKE %s"
                count_sql += " AND ISSUING_AGENCY LIKE %s"
                params.append(f"%{query.issuingAgency}%")
                count_params.append(f"%{query.issuingAgency}%")
            if query.publishTimeStart:
                sql += " AND PUBLISH_TIME >= %s"
                count_sql += " AND PUBLISH_TIME >= %s"
                params.append(query.publishTimeStart)
                count_params.append(query.publishTimeStart)
            if query.publishTimeEnd:
                sql += " AND PUBLISH_TIME <= %s"
                count_sql += " AND PUBLISH_TIME <= %s"
                params.append(query.publishTimeEnd)
                count_params.append(query.publishTimeEnd)
            if query.endTimeStart:
                sql += " AND END_TIME >= %s"
                count_sql += " AND END_TIME >= %s"
                params.append(query.endTimeStart)
                count_params.append(query.endTimeStart)
            if query.endTimeEnd:
                sql += " AND END_TIME <= %s"
                count_sql += " AND END_TIME <= %s"
                params.append(query.endTimeEnd)
                count_params.append(query.endTimeEnd)
            if query.isUse:
                sql += " AND IS_USE = %s"
                count_sql += " AND IS_USE = %s"
                params.append(query.isUse)
                count_params.append(query.isUse)
            # 分页
            offset = (query.pageNum - 1) * query.pageSize
            sql += " ORDER BY PUBLISH_TIME DESC LIMIT %s OFFSET %s"
            params.extend([query.pageSize, offset])
            # 查询总数
            cursor.execute(count_sql, count_params)
            total = cursor.fetchone()[0]
            # 查询数据
            cursor.execute(sql, params)
            rows = cursor.fetchall()
            columns = [col[0] for col in cursor.description]
            result = [dict(zip(columns, row)) for row in rows]
            return {
                "status": 200,
                "msg": "成功",
                "data": result,
                "total": total,
                "pageNum": query.pageNum,
                "pageSize": query.pageSize
            }
    except Exception as e:
        logger.error(f"查询失败: {e}")
        return {"status": 500, "msg": "失败"}
    finally:
        conn.close()
```

### 3. 说明

- 这样前端可以传 pageNum、pageSize，后端返回分页数据和总条数，便于前端分页展示。
- 你可以根据需要调整默认的 pageNum/pageSize。

需要我帮你直接修改 main.py 吗？

---

_**User**_

是 

---

_**Assistant**_

```diff
      endTimeEnd: Optional[str] = None
      isUse: Optional[int] = None    # 1: 启用，9: 弃用
+     pageNum: int = 1
+     pageSize: int = 10
  
  class PolicyTaskList(BaseModel):
```

```diff
  
  @app.post('/policy/{task_id}')
- def list_policy(task_id: str,query: PolicyQuery = Body(...)):
+ def list_policy(task_id: str, query: PolicyQuery = Body(...)):
  
      conn = get_conn()
```

```diff
              params = []
              sql = "SELECT * FROM ptl_policy_info WHERE 1=1 AND TASK_ID = %s"
+             count_sql = "SELECT COUNT(*) FROM ptl_policy_info WHERE 1=1 AND TASK_ID = %s"
              params.append(task_id)
+             count_params = [task_id]
              if query.title:
                  sql += " AND TITLE LIKE %s"
+                 count_sql += " AND TITLE LIKE %s"
                  params.append(f"%{query.title}%")
+                 count_params.append(f"%{query.title}%")
              if query.issuingAgency:
                  sql += " AND ISSUING_AGENCY LIKE %s"
+                 count_sql += " AND ISSUING_AGENCY LIKE %s"
                  params.append(f"%{query.issuingAgency}%")
+                 count_params.append(f"%{query.issuingAgency}%")
              if query.publishTimeStart:
                  sql += " AND PUBLISH_TIME >= %s"
+                 count_sql += " AND PUBLISH_TIME >= %s"
                  params.append(query.publishTimeStart)
+                 count_params.append(query.publishTimeStart)
              if query.publishTimeEnd:
                  sql += " AND PUBLISH_TIME <= %s"
+                 count_sql += " AND PUBLISH_TIME <= %s"
                  params.append(query.publishTimeEnd)
+                 count_params.append(query.publishTimeEnd)
              if query.endTimeStart:
                  sql += " AND END_TIME >= %s"
+                 count_sql += " AND END_TIME >= %s"
                  params.append(query.endTimeStart)
+                 count_params.append(query.endTimeStart)
              if query.endTimeEnd:
                  sql += " AND END_TIME <= %s"
+                 count_sql += " AND END_TIME <= %s"
                  params.append(query.endTimeEnd)
+                 count_params.append(query.endTimeEnd)
              if query.isUse:
                  sql += " AND IS_USE = %s"
+                 count_sql += " AND IS_USE = %s"
                  params.append(query.isUse)
+                 count_params.append(query.isUse)
+             # 分页
+             offset = (query.pageNum - 1) * query.pageSize
+             sql += " ORDER BY PUBLISH_TIME DESC LIMIT %s OFFSET %s"
+             params.extend([query.pageSize, offset])
+             # 查询总数
+             cursor.execute(count_sql, count_params)
+             total = cursor.fetchone()[0]
+             # 查询数据
              cursor.execute(sql, params)
              rows = cursor.fetchall()
              columns = [col[0] for col in cursor.description]
              result = [dict(zip(columns, row)) for row in rows]
-             return {"status": 200, "msg": "成功", "data": result}
+             return {
+                 "status": 200,
+                 "msg": "成功",
+                 "data": result,
+                 "total": total,
+                 "pageNum": query.pageNum,
+                 "pageSize": query.pageSize
+             }
      except Exception as e:
          logger.error(f"查询失败: {e}")
```

---

分页功能已为 `/policy/{task_id}` 接口添加完毕。  
你现在可以通过传递 `pageNum` 和 `pageSize` 参数进行分页查询，返回结果会包含 `total`、`pageNum`、`pageSize` 字段，便于前端分页展示。

如需进一步自定义分页逻辑或返回结构，请随时告知！

---

